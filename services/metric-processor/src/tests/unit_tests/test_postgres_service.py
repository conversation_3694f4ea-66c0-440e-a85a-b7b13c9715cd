"""
Unit tests for the PostgresService class.
"""

import unittest
from unittest.mock import patch, MagicMock
import os
from src.services.postgres_service import PostgresService


class TestPostgresService(unittest.TestCase):
    """Unit tests for the PostgresService class."""

    def setUp(self):
        """Set up test fixtures."""
        self.tenant = "test-tenant"
        is_local = os.getenv("LOCALDEV", "false").lower() == "true"

        # Use the correct format based on environment
        if is_local:
            self.mock_secret_data = {
                "POSTGRES_HOST": "test-host",
                "POSTGRES_USER": "test-user",
                "POSTGRES_PASSWORD": "test-password",
                "POSTGRES_PORT": "5432",
            }
        else:
            self.mock_secret_data = {
                "host": "test-host",
                "username": "test-user",
                "password": "test-password",
                "port": "5432",
            }

        # Mock the connection pool
        self.mock_pool = MagicMock()
        self.mock_conn = MagicMock()
        self.mock_cursor = MagicMock()

        # Set up the mock chain
        self.mock_pool.getconn.return_value = self.mock_conn
        self.mock_conn.cursor.return_value = self.mock_cursor
        self.mock_conn.__enter__.return_value = self.mock_conn
        self.mock_conn.__exit__.return_value = None
        self.mock_cursor.__enter__.return_value = self.mock_cursor
        self.mock_cursor.__exit__.return_value = None

        # Patch the SimpleConnectionPool to return our mock
        self.pool_patcher = patch(
            "psycopg2.pool.SimpleConnectionPool", return_value=self.mock_pool
        )
        self.mock_pool_class = self.pool_patcher.start()

    def tearDown(self):
        """Clean up after each test."""
        self.pool_patcher.stop()

    def test_initialization(self):
        """Test successful initialization of PostgresService."""
        service = PostgresService(self.mock_secret_data, self.tenant)

        self.assertEqual(service.tenant, self.tenant)
        self.assertEqual(service.connection_pool, self.mock_pool)
        self.mock_pool_class.assert_called_once()

    def test_initialization_failure(self):
        """Test initialization failure when connection fails."""
        self.mock_pool_class.side_effect = Exception("Connection failed")

        with self.assertRaises(Exception):
            PostgresService(self.mock_secret_data, self.tenant)

    def test_get_connection(self):
        """Test getting a connection from the pool."""
        service = PostgresService(self.mock_secret_data, self.tenant)
        service.connection_pool = self.mock_pool

        with service.get_connection() as conn:
            self.assertEqual(conn, self.mock_conn)
            self.assertGreaterEqual(self.mock_pool.getconn.call_count, 1)

    def test_get_connection_no_pool(self):
        """Test getting a connection when pool is not initialized."""
        service = PostgresService(self.mock_secret_data, self.tenant)
        service.connection_pool = None

        with self.assertRaises(ConnectionError):
            with service.get_connection():
                pass

    def test_release_connection(self):
        """Test releasing a connection back to the pool."""
        service = PostgresService(self.mock_secret_data, self.tenant)
        service.connection_pool = self.mock_pool

        # Reset the mock to clear calls from service initialization
        self.mock_pool.putconn.reset_mock()

        # Test that connection is automatically released when using context manager
        with service.get_connection() as conn:
            self.assertEqual(conn, self.mock_conn)

        # Verify connection was returned to pool
        self.mock_pool.putconn.assert_called_once_with(self.mock_conn)

    def test_is_connected(self):
        """Test connection status check."""
        service = PostgresService(self.mock_secret_data, self.tenant)
        service.connection_pool = self.mock_pool

        self.assertTrue(service.is_connected())

        service.connection_pool = None
        self.assertFalse(service.is_connected())

    def test_close(self):
        """Test closing all connections."""
        service = PostgresService(self.mock_secret_data, self.tenant)
        service.connection_pool = self.mock_pool

        service.close()
        self.mock_pool.closeall.assert_called_once()
        self.assertIsNone(service.connection_pool)

    def test_execute_query_with_results(self):
        """Test executing a query that returns results."""
        service = PostgresService(self.mock_secret_data, self.tenant)
        service.connection_pool = self.mock_pool

        # Mock query results
        expected_results = [("row1",), ("row2",)]
        self.mock_cursor.fetchall.return_value = expected_results
        self.mock_cursor.description = ["column1"]  # Simulate a result set

        self.mock_cursor.reset_mock()  # Reset after service creation
        results = service.execute_query("SELECT * FROM test")
        self.mock_cursor.execute.assert_called_once_with("SELECT * FROM test", None)
        self.assertEqual(results, expected_results)

    def test_execute_query_with_params(self):
        """Test executing a query with parameters."""
        service = PostgresService(self.mock_secret_data, self.tenant)
        service.connection_pool = self.mock_pool

        params = ("param1", "param2")
        self.mock_cursor.reset_mock()  # Reset after service creation
        service.execute_query("SELECT * FROM test WHERE id = %s AND name = %s", params)

        self.mock_cursor.execute.assert_called_once_with(
            "SELECT * FROM test WHERE id = %s AND name = %s", params
        )

    def test_execute_query_no_results(self):
        """Test executing a query that doesn't return results."""
        service = PostgresService(self.mock_secret_data, self.tenant)
        service.connection_pool = self.mock_pool

        # Simulate a query that doesn't return results (e.g., INSERT)
        self.mock_cursor.description = None
        self.mock_cursor.reset_mock()  # Reset after service creation
        result = service.execute_query("INSERT INTO test VALUES (1)")

        self.assertIsNone(result)
        self.mock_cursor.execute.assert_called_once_with(
            "INSERT INTO test VALUES (1)", None
        )


if __name__ == "__main__":
    unittest.main()

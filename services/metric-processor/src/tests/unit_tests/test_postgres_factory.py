"""
Unit tests for the PostgresFactory class.
"""

import unittest
from unittest.mock import patch, MagicMock
import os
import json
from src.services.postgres_factory import PostgresFactory


class TestPostgresFactory(unittest.TestCase):
    """Unit tests for the PostgresFactory class."""

    def setUp(self):
        """Set up test fixtures."""
        self.tenant = "test-tenant"
        self.mock_secret_data = {
            "POSTGRES_HOST": "test-host",
            "POSTGRES_USER": "test-user",
            "POSTGRES_PASSWORD": "test-password",
            "POSTGRES_PORT": "5432",
        }
        # Clear the factory's instance cache before each test
        PostgresFactory.clear_cache()
        # Save original environment
        self._original_env = os.environ.copy()

    def tearDown(self):
        """Clean up after each test."""
        PostgresFactory.clear_cache()
        # Restore environment
        os.environ.clear()
        os.environ.update(self._original_env)

    @patch("src.services.postgres_factory.PostgresFactory._fetch_secret")
    @patch("src.services.postgres_factory.PostgresService")
    def test_get_instance_success(self, mock_postgres_service, mock_fetch_secret):
        """Test successful instance creation and retrieval."""
        # Mock the secret fetch
        mock_fetch_secret.return_value = self.mock_secret_data

        # Mock the PostgresService instance
        mock_service = MagicMock()
        mock_service.is_connected.return_value = True
        mock_postgres_service.return_value = mock_service

        # Get instance
        instance = PostgresFactory.get_instance(self.tenant)

        # Verify
        self.assertEqual(instance, mock_service)
        mock_fetch_secret.assert_called_once()
        mock_postgres_service.assert_called_once_with(
            self.mock_secret_data, self.tenant
        )

    @patch("src.services.postgres_factory.PostgresFactory._fetch_secret")
    @patch("src.services.postgres_factory.PostgresService")
    def test_get_instance_no_secret(self, mock_postgres_service, mock_fetch_secret):
        """Test instance creation when secret fetch fails."""
        mock_fetch_secret.return_value = None

        instance = PostgresFactory.get_instance(self.tenant)

        self.assertIsNone(instance)
        mock_fetch_secret.assert_called_once()
        mock_postgres_service.assert_not_called()

    @patch("src.services.postgres_factory.PostgresFactory._fetch_secret")
    @patch("src.services.postgres_factory.PostgresService")
    def test_get_instance_connection_failure(
        self, mock_postgres_service, mock_fetch_secret
    ):
        """Test instance creation when connection fails."""
        mock_fetch_secret.return_value = self.mock_secret_data
        mock_service = MagicMock()
        mock_service.is_connected.return_value = False
        mock_postgres_service.return_value = mock_service

        instance = PostgresFactory.get_instance(self.tenant)

        self.assertIsNone(instance)
        mock_fetch_secret.assert_called_once()
        mock_postgres_service.assert_called_once_with(
            self.mock_secret_data, self.tenant
        )

    @patch("src.services.postgres_factory.PostgresFactory._fetch_secret")
    @patch("src.services.postgres_factory.PostgresService")
    def test_get_instance_caching(self, mock_postgres_service, mock_fetch_secret):
        """Test that instances are properly cached."""
        mock_fetch_secret.return_value = self.mock_secret_data
        mock_service = MagicMock()
        mock_service.is_connected.return_value = True
        mock_postgres_service.return_value = mock_service

        # Get instance twice
        instance1 = PostgresFactory.get_instance(self.tenant)
        instance2 = PostgresFactory.get_instance(self.tenant)

        # Verify
        self.assertEqual(instance1, instance2)
        mock_fetch_secret.assert_called_once()  # Secret should only be fetched once
        mock_postgres_service.assert_called_once()  # Service should only be created once

    @patch("src.services.postgres_factory.PostgresFactory._fetch_secret")
    @patch("src.services.postgres_factory.PostgresService")
    def test_close_instance(self, mock_postgres_service, mock_fetch_secret):
        """Test closing a specific instance."""
        mock_fetch_secret.return_value = self.mock_secret_data
        mock_service = MagicMock()
        mock_service.is_connected.return_value = True
        mock_postgres_service.return_value = mock_service

        # Get and then close instance
        instance = PostgresFactory.get_instance(self.tenant)
        self.assertIsNotNone(instance)
        PostgresFactory.close_instance(self.tenant)

        # Verify the instance was closed
        mock_service.close.assert_called_once()

        # Verify the instance is no longer cached by trying to get it again
        # This should create a new instance (indicated by additional calls to mocks)
        mock_fetch_secret.reset_mock()
        mock_postgres_service.reset_mock()
        mock_service_new = MagicMock()
        mock_service_new.is_connected.return_value = True
        mock_postgres_service.return_value = mock_service_new

        instance_after_close = PostgresFactory.get_instance(self.tenant)
        self.assertIsNotNone(instance_after_close)
        mock_fetch_secret.assert_called_once()  # Should be called again for new instance
        mock_postgres_service.assert_called_once()  # Should create new instance

    @patch("src.services.postgres_factory.PostgresFactory._fetch_secret")
    @patch("src.services.postgres_factory.PostgresService")
    def test_clear_cache(self, mock_postgres_service, mock_fetch_secret):
        """Test clearing all instances."""
        # Override tearDown for this test to avoid double clear_cache
        self.tearDown = lambda: None
        mock_fetch_secret.return_value = self.mock_secret_data
        mock_service = MagicMock()
        mock_service.is_connected.return_value = True
        mock_postgres_service.return_value = mock_service

        # Create multiple instances
        instance1 = PostgresFactory.get_instance("tenant1")
        instance2 = PostgresFactory.get_instance("tenant2")
        instance1.close.reset_mock()
        instance2.close.reset_mock()

        # Clear cache
        PostgresFactory.clear_cache()

        # Verify instances were closed
        self.assertGreaterEqual(instance1.close.call_count, 1)
        self.assertGreaterEqual(instance2.close.call_count, 1)

        # Verify cache is cleared by trying to get instances again
        # This should create new instances (indicated by additional calls to mocks)
        mock_fetch_secret.reset_mock()
        mock_postgres_service.reset_mock()
        mock_service_new = MagicMock()
        mock_service_new.is_connected.return_value = True
        mock_postgres_service.return_value = mock_service_new

        new_instance1 = PostgresFactory.get_instance("tenant1")
        new_instance2 = PostgresFactory.get_instance("tenant2")

        # Should have created new instances
        self.assertIsNotNone(new_instance1)
        self.assertIsNotNone(new_instance2)
        self.assertEqual(
            mock_fetch_secret.call_count, 2
        )  # Called for each new instance
        self.assertEqual(
            mock_postgres_service.call_count, 2
        )  # Created each new instance

    @patch("src.services.postgres_factory.secretmanager.SecretManagerServiceClient")
    @patch("src.services.postgres_factory.PostgresService")
    def test_fetch_secret_cloud(self, mock_postgres_service, mock_secret_client):
        """Test fetching secret from Cloud Secret Manager through get_instance."""
        # Ensure LOCALDEV is not set
        os.environ.pop("LOCALDEV", None)
        os.environ["POSTGRES_SOURCE_PROJECT_ID"] = "test-project"
        os.environ["POSTGRES_SOURCE_SECRET_NAME"] = "test-secret"

        # Mock secret client
        mock_client = MagicMock()
        mock_secret_client.return_value = mock_client

        # Mock secret response
        mock_response = MagicMock()
        mock_response.payload.data.decode.return_value = json.dumps(
            self.mock_secret_data
        )
        mock_client.access_secret_version.return_value = mock_response

        # Mock PostgresService
        mock_service = MagicMock()
        mock_service.is_connected.return_value = True
        mock_postgres_service.return_value = mock_service

        # Test by calling get_instance which internally calls _fetch_secret
        instance = PostgresFactory.get_instance(self.tenant)

        # Verify secret was fetched and instance was created
        self.assertIsNotNone(instance)
        mock_client.access_secret_version.assert_called_once()
        mock_postgres_service.assert_called_once_with(
            self.mock_secret_data, self.tenant
        )

    @patch.dict(os.environ, {"LOCALDEV": "true"})
    @patch("src.services.postgres_factory.PostgresService")
    def test_fetch_secret_local(self, mock_postgres_service):
        """Test fetching secret in local development mode through get_instance."""
        # Set up environment variables
        os.environ["POSTGRES_HOST"] = "localhost"
        os.environ["POSTGRES_USER"] = "local-user"
        os.environ["POSTGRES_PASSWORD"] = "local-pass"
        os.environ["POSTGRES_PORT"] = "5432"

        # Mock PostgresService
        mock_service = MagicMock()
        mock_service.is_connected.return_value = True
        mock_postgres_service.return_value = mock_service

        # Test by calling get_instance which internally calls _fetch_secret
        instance = PostgresFactory.get_instance(self.tenant)

        # Verify instance was created with local environment variables
        self.assertIsNotNone(instance)
        expected_secret_data = {
            "POSTGRES_HOST": "localhost",
            "POSTGRES_USER": "local-user",
            "POSTGRES_PASSWORD": "local-pass",
            "POSTGRES_PORT": "5432",
        }
        mock_postgres_service.assert_called_once_with(expected_secret_data, self.tenant)


if __name__ == "__main__":
    unittest.main()
